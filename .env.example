APP_NAME="Tester Report"
APP_ENV=local
APP_KEY=base64:T4E6TyOQiPq7qL2aMdJpgfFhbh3OfpbuS4p2dre3N0s=
APP_DEBUG=true
APP_URL=http://testerreport.test

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=testerreport
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
CACHE_PREFIX=tester_report_
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=qaadvance.com
MAIL_PORT=465
MAIL_USERNAME=nocom
MAIL_PASSWORD=nore_83
MAIL_ENCRYPTION=ssl

MAIL_FROM_ADDRESS="nocom"
MAIL_FROM_NAME="${APP_NAME}"
MAIL_REPLY_TO_ADDRESS="nocom"
MAIL_REPLY_TO_NAME="${APP_NAME} Support"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

RAY_HOST=ray@127.0.0.1  # Ray server host (Current HTTP buggregator port)
RAY_PORT=8000           # Ray server port

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"
