function b(n,i){if(!n)return n;if(n=String(n),n.length>i){const s=n.replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;");return'<span title="'+s+'" class="truncated-text" data-full-text="'+s+'">'+n.substring(0,i)+"...</span>"}const l=n.replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;");return'<span title="'+l+'" class="full-text" data-full-text="'+l+'">'+n+"</span>"}document.addEventListener("DOMContentLoaded",function(){let n="",i="";l();function l(){s(),g(),v(),x(),d(),w(),_()}function s(){$("#reportForm").on("submit",function(e){e.preventDefault(),showLoading();let t=new FormData(this);$(".report-form-submit").prop("disabled",!0),fetch(reportStoreRoute,{method:"POST",body:t,headers:{"X-CSRF-TOKEN":csrfToken}}).then(a=>{const r=a.headers.get("content-type");return r&&r.includes("application/json")?a.json():a.text().then(o=>{throw new Error("Invalid JSON: "+o)})}).then(a=>{a.success?($("#report-input-modal").modal("hide"),$("#closeModalbtn").click(),$("#reportForm")[0].reset(),$(".report-form-submit").prop("disabled",!1),hideLoading(),$("#reports-table").DataTable().ajax.reload(),Swal.fire({title:"Success!",text:"Report has been saved successfully.",icon:"success",confirmButtonText:"OK"})):(console.log(a.errors),$(".report-form-submit").prop("disabled",!1),hideLoading(),Swal.fire({title:"Error!",text:"There was an error saving the report.",icon:"error",confirmButtonText:"OK"}))}).catch(a=>{console.error("Error: ",a),hideLoading(),$(".report-form-submit").prop("disabled",!1),Swal.fire({title:"Error!",text:"There was an error processing your request.",icon:"error",confirmButtonText:"OK"})})})}function g(){$(document).on("click",".deleteReport",function(e){e.preventDefault();let a="/report/"+$(this).data("id");Swal.fire({title:"Want to Delete?",text:"This will delete the selected Report!",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, delete it!",cancelButtonText:"No, cancel!",reverseButtons:!0,customClass:{confirmButton:"btn btn-danger ml-3",cancelButton:"btn btn-secondary mr-2"},buttonsStyling:!1}).then(r=>{r.isConfirmed?$.ajax({url:a,type:"DELETE",data:{_token:csrfToken},beforeSend:function(){showLoading()},success:function(o){$("#reports-table").DataTable().ajax.reload(),hideLoading(),Swal.fire({title:"Deleted!",text:"Report has been deleted successfully.",icon:"success",confirmButtonText:"OK"})},error:function(o){hideLoading(),Swal.fire({title:"Error!",text:"There was an issue deleting the report.",icon:"error",confirmButtonText:"OK"})}}):r.dismiss===Swal.DismissReason.cancel&&Swal.fire({title:"Cancelled",text:"Your report is safe :)",icon:"info",confirmButtonText:"OK"})})})}function v(){$(document).on("click",".editReport",function(e){e.preventDefault();let t=$(this).data("id");var a=userRoleGlobal;$.ajaxSetup({headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")}}),$.ajax({url:reportUpdateRoute,method:"patch",data:{id:t},beforeSend:function(){showLoading()},success:function(r){hideLoading();var o=r[1];$("#report-input-modal").modal("show"),$("#id").val(o.id),a!="user"&&$("#user_id").val(o.user_id),$("#project_id").val(o.project_id),$("#task_tested").val(o.task_tested),$("#bug_reported").val(o.bug_reported),$("#other").val(o.other),$("#description").val(o.description),$("#regression").prop("checked",o.regression),$("#smoke_testing").prop("checked",o.smoke_testing),$("#client_meeting").prop("checked",o.client_meeting),$("#daily_meeting").prop("checked",o.daily_meeting),$("#mobile_testing").prop("checked",o.mobile_testing),$("#automation").prop("checked",o.automation)},error:function(r,o){console.log("Error "+o),hideLoading(),Swal.fire({title:"Error!",text:"There was an error loading the report data.",icon:"error",confirmButtonText:"OK"})}})})}function x(){$("#task_tested, #bug_reported").on("input",function(){let e=$(this).val();e<0?$(this).val(""):$(this).val(Math.floor(e))})}function d(){$.fn.DataTable.isDataTable("#reports-table")&&$("#reports-table").DataTable().clear().destroy(),$("#reports-table").DataTable({serverSide:!0,dom:'<"top" f> rtlp',responsive:!1,paging:!0,ajax:{url:reportsDataRoute,data:function(e){e.from_date=n,e.to_date=i,e.user_id=$("#user-name").val(),e.project_id=$("#project-name").val()}},columns:[{data:"date",name:"date",width:"150px",orderable:!1,searchable:!1},{data:"user_name",name:"user_name",searchable:!0,width:"120px"},{data:"project_name",name:"project_name",searchable:!0,width:"120px"},{data:"task_tested",name:"task_tested",width:"80px"},{data:"bug_reported",name:"bug_reported",orderable:!1,searchable:!1,width:"80px"},{data:"regression",name:"regression",orderable:!1,searchable:!1,width:"80px"},{data:"smoke_testing",name:"smoke_testing",orderable:!1,searchable:!1,width:"80px"},{data:"client_meeting",name:"client_meeting",orderable:!1,searchable:!1,width:"80px"},{data:"daily_meeting",name:"daily_meeting",orderable:!1,searchable:!1,width:"80px"},{data:"mobile_testing",name:"mobile_testing",orderable:!1,searchable:!1,width:"80px"},{data:"automation",name:"automation",orderable:!1,searchable:!1,width:"80px"},{data:"other",name:"other",orderable:!1,searchable:!1,width:"80px"},{data:"description",name:"description",width:"200px"},{data:"action",name:"action",orderable:!1,searchable:!1,width:"100px"}],columnDefs:[{targets:"_all",className:"dt-head-center dt-body-center",createdCell:function(e){$(e).css({"text-align":"center","vertical-align":"middle","white-space":"nowrap",overflow:"hidden","text-overflow":"ellipsis"})}},{targets:[2],render:function(e,t,a){return t==="display"||t==="type"?b(e,20):e}},{targets:[12],render:function(e,t,a){return t==="display"||t==="type"?b(e,20):e}}],drawCallback:function(){var e=this.api();function t(m){var h=0;if(e.column(m).data().length>0)return e.column(m).data().each(function(k){var D=parseFloat(k)||0;h+=D}),h}const a=t(3),r=t(4),o=t(5),c=t(6),u=t(7),f=t(8),p=t(9);$(e.column(3).footer()).html(a!==void 0?a:"0"),$(e.column(4).footer()).html(r!==void 0?r:"0"),$(e.column(5).footer()).html(o!==void 0?o:"0"),$(e.column(6).footer()).html(c!==void 0?c:"0"),$(e.column(7).footer()).html(u!==void 0?u:"0"),$(e.column(8).footer()).html(f!==void 0?f:"0"),$(e.column(9).footer()).html(p!==void 0?p:"0")},initComplete:function(){}})}function w(){$("#date-filter").on("change",function(){if($(this).val()==null||$(this).val()==="")return;$("#user-name").val(""),$("#project-name").val(""),$("#from-date").val(""),$("#to-date").val("");const e=new Date,t=e.toISOString().split("T")[0];e.setDate(e.getDate()-1);const a=e.toISOString().split("T")[0];n=$(this).val(),a===n?i=a:i=t,d()}),$("#date-search-btn").click(function(){$("#date-filter").val(""),n=$("#from-date").val(),i=$("#to-date").val(),d()}),$("#date-reset-btn").click(function(){$("#user-name").val(""),$("#project-name").val(""),$("#date-filter").val(""),$("#from-date").val(""),$("#to-date").val(""),n=i="",d()})}function _(){$("#report-input-modal").on("hidden.bs.modal",function(){$("#reportForm")[0].reset(),$(".report-form-submit").removeClass("disabled")})}function y(){$(document).on("mouseenter","#reports-table td:nth-child(3) span[title], #reports-table td:nth-child(13) span[title]",function(e){const t=$(this),a=t.attr("title")||t.data("full-text");a&&a.length>20&&(t.addClass("has-tooltip"),t.css({transform:"scale(1.02)",transition:"transform 0.2s ease"}))}),$(document).on("mouseleave","#reports-table td:nth-child(3) span[title], #reports-table td:nth-child(13) span[title]",function(){$(this).removeClass("has-tooltip").css({transform:"scale(1)",transition:"transform 0.2s ease"})}),$(document).on("dblclick","#reports-table td:nth-child(3) .truncated-text, #reports-table td:nth-child(13) .truncated-text",function(e){e.preventDefault();const t=$(this).attr("title")||$(this).data("full-text"),a=$(this).closest("td").is(":nth-child(3)")?"Project":"Description";t&&t.length>50&&T(a,t)})}function T(e,t){const a=`
            <div class="modal fade" id="contentModal" tabindex="-1" role="dialog" aria-labelledby="contentModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title" id="contentModalLabel">
                                <i class="fas fa-info-circle mr-2"></i>${e} Content
                            </h5>
                            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="content-display p-3" style="background-color: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6;">
                                ${t}
                            </div>
                            <div class="mt-3 text-muted small">
                                <i class="fas fa-lightbulb mr-1"></i>
                                Tip: You can copy this text by selecting it.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">
                                <i class="fas fa-times mr-1"></i>Close
                            </button>
                            <button type="button" class="btn btn-primary" onclick="copyToClipboard('${t.replace(/'/g,"\\'")}')">
                                <i class="fas fa-copy mr-1"></i>Copy Text
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;$("#contentModal").remove(),$("body").append(a),$("#contentModal").modal("show"),$("#contentModal").on("hidden.bs.modal",function(){$(this).remove()})}$(document).ready(function(){y()})});
