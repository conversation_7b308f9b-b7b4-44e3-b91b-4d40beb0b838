/* Column width and height control */
.column-width-height {
    text-wrap: nowrap;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: all 0.3s ease;
}

.column-width-height:hover {
    text-wrap: wrap;
    max-width: 300px;
    background-color: #f8f9fa;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
    position: relative;
}

/* DataTable filter styling */
#reports-table_filter {
    margin: 0.5rem;
}

#reports-table_filter label {
    margin-top: -0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

#reports-table_filter input {
    border-radius: var(--border-radius);
    padding: 0.375rem 0.75rem;
    border: 1px solid var(--border-color);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

#reports-table_filter input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    outline: 0;
}

/* Date filters styling */
.table-container .dates input {
    border-radius: var(--border-radius);
    padding: 0.375rem 0.75rem;
}

.table-container .dates {
    margin-bottom: -2.5rem;
    margin-top: 0.5rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

/* DataTables wrapper styling */
.dataTables_wrapper {
    margin: 0;
    width: 100%;
}

/* Length control styling */
#reports-table_length {
    margin-top: 0.75rem;
}

#reports-table_length select {
    border-radius: 0.75rem;
    padding: 0.375rem 2rem 0.375rem 0.75rem;
    border: 1px solid var(--border-color);
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    appearance: none;
}

/* Pagination styling */
#reports-table_paginate .paginate_button {
    padding: 0.375rem 0.75rem;
    margin: 0 0.25rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

#reports-table_paginate .paginate_button.current {
    border-radius: 0.5rem;
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
    margin-top: 0.5rem;
}

#reports-table_paginate .paginate_button:hover:not(.current) {
    background-color: #e9ecef !important;
    border-color: #dee2e6 !important;
    color: var(--secondary-color) !important;
}

/* Form controls styling */
input, .form-control, textarea {
    border-radius: var(--border-radius) !important;
    margin-top: 0.25rem;
    padding: 0.375rem 0.75rem;
    border: 1px solid var(--border-color);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

input:focus, .form-control:focus, textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    outline: 0;
}

/* Modal footer styling */
.modal-footer {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    padding: 1rem;
    border-top: 1px solid var(--border-color);
}

/* Button styling */
.btn {
    border-radius: var(--border-radius);
    padding: 0.375rem 0.75rem;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .table-container .dates {
        flex-direction: column;
        margin-bottom: 1rem;
    }

    .column-width-height:hover {
        max-width: 250px;
    }

    #reports-table_filter, #reports-table_length {
        text-align: left;
        margin-left: 0;
    }
}

/* Styling for truncated text columns */
#reports-table td:nth-child(3), /* Project column */
#reports-table td:nth-child(13) /* Description column */ {
    max-width: 150px !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    position: relative;
}

/* Tooltip styling for truncated text */
#reports-table td span[title],
#reports-table td .truncated-text,
#reports-table td .full-text {
    cursor: help;
    display: inline-block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: inherit;
    text-decoration: none;
    position: relative;
}

/* Enhanced tooltip on hover */
#reports-table td span[title]:hover,
#reports-table td .truncated-text:hover,
#reports-table td .full-text:hover {
    position: relative;
    z-index: 1000;
    background-color: #e3f2fd;
    border-radius: 4px;
    padding: 3px 6px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Custom tooltip styling using CSS */
#reports-table td span[title]:hover::after,
#reports-table td .truncated-text:hover::after,
#reports-table td .full-text:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    white-space: normal;
    max-width: 350px;
    min-width: 200px;
    word-wrap: break-word;
    z-index: 1001;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    margin-bottom: 8px;
    pointer-events: none;
    line-height: 1.5;
    text-align: left;
    border: 1px solid rgba(255,255,255,0.1);
    animation: tooltipFadeIn 0.3s ease-out;
}

/* Tooltip arrow */
#reports-table td span[title]:hover::before,
#reports-table td .truncated-text:hover::before,
#reports-table td .full-text:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 8px solid transparent;
    border-top-color: #2c3e50;
    z-index: 1001;
    margin-bottom: -8px;
    pointer-events: none;
}

/* Tooltip fade-in animation */
@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* Special styling for Project and Description columns */
#reports-table td:nth-child(3) span[title]:hover::after, /* Project column */
#reports-table td:nth-child(13) span[title]:hover::after /* Description column */ {
    background: linear-gradient(135deg, #1e88e5 0%, #1976d2 100%);
    border-color: rgba(255,255,255,0.2);
}

#reports-table td:nth-child(3) span[title]:hover::before, /* Project column */
#reports-table td:nth-child(13) span[title]:hover::before /* Description column */ {
    border-top-color: #1e88e5;
}

/* Responsive tooltip adjustments */
@media (max-width: 768px) {
    #reports-table td span[title]:hover::after,
    #reports-table td .truncated-text:hover::after,
    #reports-table td .full-text:hover::after {
        max-width: 250px;
        min-width: 150px;
        font-size: 12px;
        padding: 10px 12px;
    }
}

/* Enhanced visual feedback for interactive elements */
#reports-table td:nth-child(3) span[title],
#reports-table td:nth-child(13) span[title] {
    border-bottom: 1px dotted transparent;
    transition: all 0.3s ease;
}

#reports-table td:nth-child(3) span[title]:hover,
#reports-table td:nth-child(13) span[title]:hover {
    border-bottom-color: #007bff;
    cursor: pointer;
}

/* Visual indicator for truncated text */
.truncated-text::after {
    content: ' 📄';
    font-size: 10px;
    opacity: 0.6;
    margin-left: 2px;
}

.truncated-text:hover::after {
    opacity: 1;
}

/* Style for has-tooltip class */
.has-tooltip {
    background-color: rgba(0, 123, 255, 0.1) !important;
    border-radius: 3px !important;
    box-shadow: 0 0 0 1px rgba(0, 123, 255, 0.3) !important;
}

/* Double-click hint */
.truncated-text {
    position: relative;
}

.truncated-text:hover::before {
    content: 'Double-click for full content';
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    white-space: nowrap;
    z-index: 1000;
    opacity: 0;
    animation: hintFadeIn 0.5s ease-in-out 1s forwards;
    pointer-events: none;
}

@keyframes hintFadeIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-5px);
    }
    to {
        opacity: 0.8;
        transform: translateX(-50%) translateY(0);
    }
}