{"name": "your-vendor/tester-report", "description": "A project for generating user reports.", "type": "project", "require": {"php": "^7.3|^8.0", "laravel/framework": "^8.0|^9.0", "fideloper/proxy": "^4.4", "fruitcake/laravel-cors": "^2.0", "laravel/tinker": "^2.0"}, "require-dev": {"phpunit/phpunit": "^9.0", "mockery/mockery": "^1.4", "laravel/sail": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "php artisan package:discover --ansi"], "test": "phpunit"}, "minimum-stability": "stable", "prefer-stable": true}